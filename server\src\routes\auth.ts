import { Hono } from "hono";
import { cors } from "hono/cors";
import type { AuthType } from "../auth";
import { auth } from "../auth";

const authRouter = new Hono<{
	Bindings: AuthType;
	Variables: {
		user: any;
		session: any;
	};
}>({
	strict: false,
});

authRouter.use("*", async (c, next) => {
	const session = await auth.api.getSession({ headers: c.req.raw.headers });

	if (!session) {
		c.set("user", null);
		c.set("session", null);
		return next();
	}

	c.set("user", session.user);
	c.set("session", session.session);
	return next();
});

authRouter.use(
	"/auth/*",
	cors({
		origin: "http://localhost:5173", // replace with your origin
		allowHeaders: ["Content-Type", "Authorization"],
		allowMethods: ["POST", "GET", "OPTIONS"],
		exposeHeaders: ["Content-Length"],
		maxAge: 600,
		credentials: true,
	}),
);

authRouter.on(["POST", "GET"], "/auth/*", (c) => {
	return auth.handler(c.req.raw);
});

export default authRouter;
