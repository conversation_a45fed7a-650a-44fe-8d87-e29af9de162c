import { auth } from "@server/auth";
import { env } from "server/env";
import { db } from "./index";

const seed = async () => {
	const account = await db.query.account.findFirst();
	const user = await db.query.user.findFirst();
	if (account || user) return console.log("Database already seeded");
	const create = auth.api.signUpEmail({
		body: {
			email: `${env.ROOT_EMAIL}`,
			password: `${env.ROOT_PASSWORD}`,
			name: `${env.ROOT_USERNAME}`,
		},
	});
};

seed()
	.catch((err) => {
		console.error(err);
		process.exit(1);
	})
	.then(() => {
		console.log("Database seeded");
	})
	.finally(() => {
		process.exit(0);
	});
