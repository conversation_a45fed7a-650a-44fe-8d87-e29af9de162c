import { createFileRoute, redirect } from "@tanstack/react-router";
import { LogOut, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { authClient, useSession } from "@/lib/auth";

export const Route = createFileRoute("/dashboard")({
  component: DashboardComponent,
  beforeLoad: async () => {
    // Check if user is authenticated
    const session = await authClient.getSession();
    if (!session) {
      throw redirect({
        to: "/login",
        search: {
          redirect: "/dashboard",
        },
      });
    }
  },
});

function DashboardComponent() {
  const { data: session, isPending } = useSession();

  const handleSignOut = async () => {
    try {
      await authClient.signOut();
      // Redirect will happen automatically due to the beforeLoad check
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  if (isPending) {
    return (
      <div className="min-h-screen bg-background text-white font-primary flex items-center justify-center">
        <div className="animate-pulse">Loading...</div>
      </div>
    );
  }

  if (!session) {
    return null; // This shouldn't happen due to beforeLoad, but just in case
  }

  return (
    <div className="min-h-screen bg-background text-white font-primary p-4 pt-20">
      <div className="container mx-auto max-w-4xl">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button
            variant="outline"
            onClick={handleSignOut}
            className="flex items-center gap-2"
          >
            <LogOut size={16} />
            Sign Out
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="p-6 bg-card border-border">
            <div className="flex items-center gap-4 mb-4">
              <User size={24} />
              <h2 className="text-xl font-semibold">Profile Information</h2>
            </div>
            <div className="space-y-2">
              <div>
                <span className="text-muted-foreground">Name:</span>
                <span className="ml-2">
                  {session.user.name || "Not provided"}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Email:</span>
                <span className="ml-2">{session.user.email}</span>
              </div>
              <div>
                <span className="text-muted-foreground">User ID:</span>
                <span className="ml-2 font-mono text-sm">
                  {session.user.id}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Email Verified:</span>
                <span className="ml-2">
                  {session.user.emailVerified ? "✅ Yes" : "❌ No"}
                </span>
              </div>
            </div>
          </Card>

          <Card className="p-6 bg-card border-border">
            <h2 className="text-xl font-semibold mb-4">Session Information</h2>
            <div className="space-y-2">
              <div>
                <span className="text-muted-foreground">Session ID:</span>
                <span className="ml-2 font-mono text-sm">
                  {session.session.id}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Created:</span>
                <span className="ml-2">
                  {new Date(session.session.createdAt).toLocaleDateString()}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Expires:</span>
                <span className="ml-2">
                  {new Date(session.session.expiresAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </Card>
        </div>

        <Card className="p-6 bg-card border-border mt-6">
          <h2 className="text-xl font-semibold mb-4">
            Welcome to your Dashboard!
          </h2>
          <p className="text-muted-foreground">
            This is a protected route that requires authentication. You can only
            see this page because you're logged in. The authentication is
            handled by better-auth with automatic redirects and session
            management.
          </p>
          <div className="mt-4 p-4 bg-muted/20 rounded-lg">
            <h3 className="font-semibold mb-2">Features implemented:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Email/password authentication</li>
              <li>Protected routes with automatic redirects</li>
              <li>Session management</li>
              <li>User profile display</li>
              <li>Sign out functionality</li>
              <li>Responsive design with Tailwind CSS</li>
            </ul>
          </div>
        </Card>
      </div>
    </div>
  );
}
