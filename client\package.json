{"name": "client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@icons-pack/react-simple-icons": "^13.7.0", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-core": "^0.13.8", "@tailwindcss/vite": "^4.1.10", "@tanstack/react-form": "^1.14.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-router": "^1.128.8", "@tanstack/react-router-devtools": "^1.128.8", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.507.0", "motion": "^12.23.6", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "server": "workspace:*", "shared": "workspace:*", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.28.0", "@tanstack/router-plugin": "^1.128.8", "@tanstack/router-vite-plugin": "^1.128.8", "@types/node": "^22.15.31", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.28.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tw-animate-css": "^1.3.4", "typescript": "~5.7.3", "typescript-eslint": "^8.34.0", "vite": "^6.3.5"}}