import { createEnv } from "@t3-oss/env-core";
import { z } from "zod";

export const env = createEnv({
	server: {
		NODE_ENV: z.string().default("development"),
		DATABASE_URL: z.string().url().min(1),
		BETTER_AUTH_SECRET: z.string().min(1),
		BETTER_AUTH_URL: z.string().min(1),
		ROOT_USERNAME: z.string().min(1),
		ROOT_EMAIL: z.string().email().min(1),
		ROOT_PASSWORD: z.string().min(1),
	},
	runtimeEnv: process.env,
});
