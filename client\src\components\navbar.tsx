import { Link } from "@tanstack/react-router";
import { LogOut, User } from "lucide-react";
import { authClient, useSession } from "@/lib/auth";
import { Button } from "./ui/button";

export default function Navbar() {
    const { data: session, isPending } = useSession();

    const handleSignOut = async () => {
        try {
            await authClient.signOut();
        } catch (error) {
            console.error('Sign out error:', error);
        }
    };

    return (
        <nav className="fixed top-0 left-0 right-0 p-4 z-50">
            <div className="container mx-auto flex justify-between items-center">
                <div className="flex justify-center items-center cursor-target">
                    <img src="/logo_white.png" alt="Logo" height={36} width={36} />
                    <Link to="/" className="font-bold text-lg pr-1">&gt;ssh occo.rocks</Link>
                </div>
                <div className="flex items-center gap-4">
                    <Button variant="ghost" asChild>
                        <Link to="/about">About</Link>
                    </Button>
                    <Button variant="ghost" asChild>
                        <Link to="/projects">Projects</Link>
                    </Button>
                    <Button variant="ghost" asChild>
                        <Link to="/blog">Blog</Link>
                    </Button>
                    {session && (
                        <Button variant="ghost" asChild>
                            <Link to="/dashboard">Dashboard</Link>
                        </Button>
                    )}

                    {/* Authentication buttons */}
                    {isPending ? (
                        <div className="w-8 h-8 animate-pulse bg-gray-600 rounded"></div>
                    ) : session ? (
                        <div className="flex items-center gap-2">
                            <div className="flex items-center gap-2 text-sm">
                                <User size={16} />
                                <span className="hidden sm:inline">
                                    {session.user.name || session.user.email}
                                </span>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleSignOut}
                                className="flex items-center gap-2"
                            >
                                <LogOut size={16} />
                                <span className="hidden sm:inline">Sign Out</span>
                            </Button>
                        </div>
                    ) : (
                        <div className="flex items-center gap-2">
                            <Button variant="default" size="sm" asChild>
                                <Link to="/login">Sign In</Link>
                            </Button>
                        </div>
                    )}
                </div>
            </div>
        </nav>
    );
}
