import type { AuthType } from "@server/auth";
import { db } from "@server/db";
import { message } from "@server/db/schema";
import { Hono } from "hono";
import {
	type ApiResponse,
	type BlogResponse,
	generateId,
	type Message,
} from "shared/dist";

const apiRouter = new Hono<{
	Bindings: AuthType;
	Variables: {
		user: any;
		session: any;
	};
}>({
	strict: false,
});

apiRouter.post("/message", async (c) => {
	const request = await c.req.json<Message>();
	if (!request.message) {
		return c.json({
			success: false,
			message: "you can't enter a blank message, silly.",
		});
	}
	const saved = await db.insert(message).values({
		id: generateId(32),
		message: request.message,
	});
	if (!saved) {
		return c.json({
			success: false,
			message: "something went wrong, try again.",
		});
	}
	const response: ApiResponse = {
		success: true,
		message: "your message was sent!",
	};
	return c.json(response);
});

// blog route - get (all), post (protected)
// auth for route on post
//   async (c, next) => {
//     const user = await auth.api.getSession({headers: c.req.raw.headers });
//     return auth(c, next)
//   },
apiRouter.post("/blog", async (c) => {
	const session = c.get("session");
	const user = c.get("user");

	if (!session) return c.body(null, 401);
	if (!user) return c.body(null, 401);

	return c.json({
		success: false,
		message: "not implemented yet.",
	});
});
apiRouter.get("/blog", async (c) => {
	const blogs = await db.query.blogs.findMany();
	if (!blogs)
		return c.json({
			success: true,
			message: "no blogs found.",
		});
	const response: BlogResponse = {
		success: true,
		message: `${blogs.length} blogs found.`,
		data: blogs,
	};
	return c.json(response);
});

export default apiRouter;
