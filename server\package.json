{"name": "server", "version": "0.0.1", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "bun --watch run src/index.ts && tsc --watch", "db:studio": "bunx drizzle-kit studio", "db:push": "bunx drizzle-kit push", "db:generate": "bunx drizzle-kit generate", "db:migrate": "bunx drizzle-kit migrate", "db:seed": "bun run src/db/seed.ts"}, "dependencies": {"@t3-oss/env-core": "^0.13.8", "better-auth": "^1.3.4", "drizzle-orm": "^0.44.3", "hono": "^4.7.11", "pg": "^8.16.3", "shared": "workspace:*", "zod": "^4.0.10"}, "devDependencies": {"@types/bun": "latest", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.4", "tsx": "^4.20.3"}}